use crate::api::models::{Artwork, Novel, PageDetail};
use crate::utils::error::{<PERSON><PERSON><PERSON>, Result};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::path::Path;

/// Metadata formatter for artworks and novels
pub struct MetadataFormatter {
    date_format: String,
    tag_separator: String,
}

impl MetadataFormatter {
    /// Create a new metadata formatter
    pub fn new(date_format: String, tag_separator: String) -> Self {
        Self {
            date_format,
            tag_separator,
        }
    }

    /// Create a default metadata formatter
    pub fn new_default() -> Self {
        Self {
            date_format: "%Y-%m-%d %H:%M:%S UTC".to_string(),
            tag_separator: ", ".to_string(),
        }
    }

    /// Format artwork metadata as text
    pub fn format_artwork_metadata(
        &self,
        artwork: &Artwork,
        pages: &[PageDetail],
        download_time: DateTime<Utc>,
    ) -> String {
        let mut metadata = String::new();

        // Basic information
        metadata.push_str(&format!("Title: {}\n", artwork.title));
        metadata.push_str(&format!("Author_UID: {}\n", artwork.user_id));
        metadata.push_str(&format!("Author_Username: {}\n", artwork.user_name));
        metadata.push_str(&format!("Artwork_PID: {}\n", artwork.id));
        metadata.push_str(&format!("Artwork_Type: {}\n", artwork.artwork_type));

        // Tags
        let tags = artwork.tag_names();
        metadata.push_str(&format!("Tags: {}\n", tags.join(&self.tag_separator)));

        // Description
        metadata.push_str("Description: \n");
        if !artwork.caption.is_empty() {
            metadata.push_str(&artwork.caption);
            metadata.push('\n');
        }

        // Series information
        if let Some(series) = &artwork.series {
            if let Some(title) = series.get("title").and_then(|t| t.as_str()) {
                metadata.push_str(&format!("Series_Title: {}\n", title));
            }
            if let Some(id) = series.get("id").and_then(|i| i.as_u64()) {
                metadata.push_str(&format!("Series_ID: {}\n", id));
            }
        }

        // Dates
        metadata.push_str(&format!(
            "Upload_Date: {}\n",
            artwork.upload_date.format(&self.date_format)
        ));
        metadata.push_str(&format!(
            "Create_Date: {}\n",
            artwork.create_date.format(&self.date_format)
        ));

        // Page information
        metadata.push_str(&format!("Page_Count: {}\n", artwork.page_count.unwrap_or(1)));

        // Content rating
        metadata.push_str(&format!("R18: {}\n", artwork.is_r18()));

        // Statistics
        metadata.push_str(&format!("Like_Count: {}\n", artwork.total_view.unwrap_or(0)));
        metadata.push_str(&format!("Bookmark_Count: {}\n", artwork.total_bookmarks.unwrap_or(0)));

        // Dimensions (if available)
        if let Some(width) = artwork.width {
            metadata.push_str(&format!("Width: {}\n", width));
        }
        if let Some(height) = artwork.height {
            metadata.push_str(&format!("Height: {}\n", height));
        }

        // Original URLs
        if !pages.is_empty() {
            metadata.push_str("Original_URLs: \n");
            for (index, page) in pages.iter().enumerate() {
                metadata.push_str(&format!("  Page_{}: {}\n", index, page.image_urls.original));
            }
        }

        // Download information
        metadata.push_str(&format!(
            "Download_Time: {}\n",
            download_time.format(&self.date_format)
        ));

        metadata
    }

    /// Format novel metadata and content as text
    pub fn format_novel_content(&self, novel: &Novel, download_time: DateTime<Utc>) -> String {
        let mut content = String::new();

        // Basic information
        content.push_str(&format!("Title: {}\n", novel.title));
        content.push_str(&format!("Author_UID: {}\n", novel.user.id));
        content.push_str(&format!("Author_Username: {}\n", novel.user.name));
        content.push_str(&format!("Novel_PID: {}\n", novel.id));
        content.push_str("Novel_Type: Novel\n");

        // Dates
        content.push_str(&format!(
            "Upload_Date: {}\n",
            novel.upload_date.format(&self.date_format)
        ));
        content.push_str(&format!(
            "Create_Date: {}\n",
            novel.create_date.format(&self.date_format)
        ));

        // Tags
        let tags: Vec<String> = novel.tags.iter().map(|tag| tag.name.clone()).collect();
        content.push_str(&format!("Tags: {}\n", tags.join(&self.tag_separator)));

        // Series information
        if let Some(series) = &novel.series {
            if let Some(title) = series.get("title").and_then(|t| t.as_str()) {
                content.push_str(&format!("Series_Title: {}\n", title));
            }
            if let Some(id) = series.get("id").and_then(|i| i.as_u64()) {
                content.push_str(&format!("Series_ID: {}\n", id));
            }
        }

        // Content rating
        content.push_str(&format!("R18: {}\n", novel.is_r18()));

        // Statistics
        content.push_str(&format!("Like_Count: {}\n", novel.total_view));
        content.push_str(&format!("Bookmark_Count: {}\n", novel.total_bookmarks));
        content.push_str(&format!("Word_Count: {}\n", novel.text_length));

        // Description
        content.push_str("Description: \n");
        if !novel.caption.is_empty() {
            content.push_str(&novel.caption);
            content.push('\n');
        }

        // Download information
        content.push_str(&format!(
            "Download_Time: {}\n",
            download_time.format(&self.date_format)
        ));

        // Content separator
        content.push_str("--- Content ---\n");

        // Novel text content
        if let Some(novel_text) = &novel.content {
            content.push('\n');
            content.push_str(novel_text);
        } else {
            content.push_str("\n[Content not available]\n");
        }

        content
    }

    /// Save artwork metadata to file
    pub async fn save_artwork_metadata<P: AsRef<Path>>(
        &self,
        path: P,
        artwork: &Artwork,
        pages: &[PageDetail],
        download_time: DateTime<Utc>,
    ) -> Result<()> {
        let metadata = self.format_artwork_metadata(artwork, pages, download_time);

        tokio::fs::write(path.as_ref(), metadata)
            .await
            .map_err(|e| Error::storage(format!("Failed to save artwork metadata: {}", e)))?;

        Ok(())
    }

    /// Save novel content to file
    pub async fn save_novel_content<P: AsRef<Path>>(
        &self,
        path: P,
        novel: &Novel,
        download_time: DateTime<Utc>,
    ) -> Result<()> {
        let content = self.format_novel_content(novel, download_time);

        tokio::fs::write(path.as_ref(), content)
            .await
            .map_err(|e| Error::storage(format!("Failed to save novel content: {}", e)))?;

        Ok(())
    }

    /// Parse metadata from existing file (for updating or checking)
    pub async fn parse_metadata<P: AsRef<Path>>(&self, path: P) -> Result<HashMap<String, String>> {
        let content = tokio::fs::read_to_string(path.as_ref())
            .await
            .map_err(|e| Error::storage(format!("Failed to read metadata file: {}", e)))?;

        let mut metadata = HashMap::new();
        let mut in_description = false;
        let mut description_lines = Vec::new();

        for line in content.lines() {
            if line == "Description: " {
                in_description = true;
                continue;
            }

            if in_description {
                if line.contains(':') && !line.starts_with(' ') {
                    // End of description, start of next field
                    metadata.insert("Description".to_string(), description_lines.join("\n"));
                    description_lines.clear();
                    in_description = false;

                    // Process this line as a regular field
                    if let Some((key, value)) = line.split_once(':') {
                        metadata.insert(key.trim().to_string(), value.trim().to_string());
                    }
                } else {
                    description_lines.push(line.to_string());
                }
            } else if let Some((key, value)) = line.split_once(':') {
                metadata.insert(key.trim().to_string(), value.trim().to_string());
            }
        }

        // Handle case where description is the last field
        if in_description && !description_lines.is_empty() {
            metadata.insert("Description".to_string(), description_lines.join("\n"));
        }

        Ok(metadata)
    }

    /// Check if metadata file exists and is valid
    pub async fn is_valid_metadata<P: AsRef<Path>>(&self, path: P) -> bool {
        match self.parse_metadata(path).await {
            Ok(metadata) => {
                // Check for essential fields
                metadata.contains_key("Title")
                    && metadata.contains_key("Author_UID")
                    && (metadata.contains_key("Artwork_PID") || metadata.contains_key("Novel_PID"))
            }
            Err(_) => false,
        }
    }

    /// Get the date format
    pub fn date_format(&self) -> &str {
        &self.date_format
    }

    /// Get the tag separator
    pub fn tag_separator(&self) -> &str {
        &self.tag_separator
    }

    /// Update date format
    pub fn with_date_format(mut self, format: String) -> Self {
        self.date_format = format;
        self
    }

    /// Update tag separator
    pub fn with_tag_separator(mut self, separator: String) -> Self {
        self.tag_separator = separator;
        self
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::api::models::{ArtworkType, ImageUrls, Tag, User};
    use chrono::TimeZone;
    use std::collections::HashMap;

    fn create_test_artwork() -> Artwork {
        Artwork {
            id: 12345,
            title: "Test Artwork".to_string(),
            artwork_type: ArtworkType::Illust,
            image_urls: Some(ImageUrls {
                square_medium: None,
                medium: None,
                large: None,
                original: "https://example.com/image.jpg".to_string(),
            }),
            caption: "Test description".to_string(),
            restrict: 0,
            user: User {
                id: 67890,
                name: "Test User".to_string(),
                account: "testuser".to_string(),
                profile_image_urls: HashMap::new(),
                comment: None,
                is_followed: false,
            },
            tags: vec![
                Tag {
                    name: "tag1".to_string(),
                    translated_name: None,
                },
                Tag {
                    name: "tag2".to_string(),
                    translated_name: None,
                },
            ],
            tools: vec![],
            create_date: Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap(),
            upload_date: Utc.with_ymd_and_hms(2023, 1, 1, 12, 0, 0).unwrap(),
            page_count: 1,
            width: Some(1920),
            height: Some(1080),
            sanity_level: 2,
            x_restrict: 0,
            series: None,
            meta_single_page: None,
            meta_pages: None,
            total_view: 1000,
            total_bookmarks: 50,
            is_bookmarked: false,
            visible: true,
            is_muted: false,
            total_comments: Some(10),
        }
    }

    #[test]
    fn test_format_artwork_metadata() {
        let formatter = MetadataFormatter::new_default();
        let artwork = create_test_artwork();
        let pages = vec![];
        let download_time = Utc::now();

        let metadata = formatter.format_artwork_metadata(&artwork, &pages, download_time);

        assert!(metadata.contains("Title: Test Artwork"));
        assert!(metadata.contains("Author_UID: 67890"));
        assert!(metadata.contains("Author_Username: Test User"));
        assert!(metadata.contains("Artwork_PID: 12345"));
        assert!(metadata.contains("Tags: tag1, tag2"));
        assert!(metadata.contains("R18: false"));
    }

    #[test]
    fn test_metadata_formatter_customization() {
        let formatter = MetadataFormatter::new("%Y-%m-%d".to_string(), " | ".to_string());

        assert_eq!(formatter.date_format(), "%Y-%m-%d");
        assert_eq!(formatter.tag_separator(), " | ");
    }

    #[test]
    fn test_metadata_formatter_default() {
        let formatter = MetadataFormatter::new_default();
        assert_eq!(formatter.date_format(), "%Y-%m-%d %H:%M:%S UTC");
        assert_eq!(formatter.tag_separator(), ", ");
    }
}
