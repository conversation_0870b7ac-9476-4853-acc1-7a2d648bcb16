use crate::api::models::{Artwork, ArtworkType, Novel};
use crate::config::settings::TagLogic;
use tracing::debug;

/// Filter for artworks and novels based on various criteria
#[derive(Debug, Clone)]
pub struct ContentFilter {
    tags: Vec<String>,
    tag_logic: TagLogic,
    artwork_types: Vec<ArtworkType>,
    include_r18: bool,
}

impl ContentFilter {
    /// Create a new content filter
    pub fn new(
        tags: Vec<String>,
        tag_logic: TagLogic,
        artwork_types: Vec<ArtworkType>,
        include_r18: bool,
    ) -> Self {
        Self {
            tags: tags.into_iter().map(|tag| tag.to_lowercase()).collect(),
            tag_logic,
            artwork_types,
            include_r18,
        }
    }

    /// Create a filter that accepts all content
    pub fn accept_all(artwork_types: Vec<ArtworkType>) -> Self {
        Self {
            tags: vec![],
            tag_logic: TagLogic::Or,
            artwork_types,
            include_r18: true,
        }
    }

    /// Filter artworks based on the criteria
    pub fn filter_artworks<'a>(&self, artworks: &'a [Artwork]) -> Vec<&'a Artwork> {
        artworks
            .iter()
            .filter(|artwork| self.matches_artwork(artwork))
            .collect()
    }

    /// Filter novels based on the criteria
    pub fn filter_novels<'a>(&self, novels: &'a [Novel]) -> Vec<&'a Novel> {
        novels
            .iter()
            .filter(|novel| self.matches_novel(novel))
            .collect()
    }

    /// Check if an artwork matches the filter criteria
    pub fn matches_artwork(&self, artwork: &Artwork) -> bool {
        // Check artwork type
        // Convert artwork_type number to ArtworkType enum
        let artwork_type = match artwork.artwork_type {
            0 => ArtworkType::Illust,
            1 => ArtworkType::Manga,
            2 => ArtworkType::Ugoira,
            _ => ArtworkType::Illust, // Default to illustration
        };

        if !self.artwork_types.contains(&artwork_type) {
            debug!(
                "Artwork {} filtered out: type {} not in allowed types",
                artwork.id, artwork.artwork_type
            );
            return false;
        }

        // Check R18 content
        if artwork.is_r18() && !self.include_r18 {
            debug!(
                "Artwork {} filtered out: R18 content not allowed",
                artwork.id
            );
            return false;
        }

        // Check tags if any are specified
        if !self.tags.is_empty() {
            let matches_tags = match self.tag_logic {
                TagLogic::And => self.matches_all_tags_artwork(artwork),
                TagLogic::Or => self.matches_any_tag_artwork(artwork),
            };

            if !matches_tags {
                debug!(
                    "Artwork {} filtered out: tags don't match criteria",
                    artwork.id
                );
                return false;
            }
        }

        true
    }

    /// Check if a novel matches the filter criteria
    pub fn matches_novel(&self, novel: &Novel) -> bool {
        // Check if novels are allowed
        if !self.artwork_types.contains(&ArtworkType::Novel) {
            debug!(
                "Novel {} filtered out: novels not in allowed types",
                novel.id
            );
            return false;
        }

        // Check R18 content
        if novel.is_r18() && !self.include_r18 {
            debug!("Novel {} filtered out: R18 content not allowed", novel.id);
            return false;
        }

        // Check tags if any are specified
        if !self.tags.is_empty() {
            let matches_tags = match self.tag_logic {
                TagLogic::And => self.matches_all_tags_novel(novel),
                TagLogic::Or => self.matches_any_tag_novel(novel),
            };

            if !matches_tags {
                debug!("Novel {} filtered out: tags don't match criteria", novel.id);
                return false;
            }
        }

        true
    }

    /// Check if artwork has all required tags (AND logic)
    fn matches_all_tags_artwork(&self, artwork: &Artwork) -> bool {
        let artwork_tags: Vec<String> = artwork
            .tag_names()
            .iter()
            .map(|t| t.to_lowercase())
            .collect();
        self.tags.iter().all(|tag| artwork_tags.contains(tag))
    }

    /// Check if artwork has any of the required tags (OR logic)
    fn matches_any_tag_artwork(&self, artwork: &Artwork) -> bool {
        let artwork_tags: Vec<String> = artwork
            .tag_names()
            .iter()
            .map(|t| t.to_lowercase())
            .collect();
        self.tags.iter().any(|tag| artwork_tags.contains(tag))
    }

    /// Check if novel has all required tags (AND logic)
    fn matches_all_tags_novel(&self, novel: &Novel) -> bool {
        let novel_tags: Vec<String> = novel.tag_names().iter().map(|t| t.to_lowercase()).collect();
        self.tags.iter().all(|tag| novel_tags.contains(tag))
    }

    /// Check if novel has any of the required tags (OR logic)
    fn matches_any_tag_novel(&self, novel: &Novel) -> bool {
        let novel_tags: Vec<String> = novel.tag_names().iter().map(|t| t.to_lowercase()).collect();
        self.tags.iter().any(|tag| novel_tags.contains(tag))
    }

    /// Get the filter tags
    pub fn tags(&self) -> &[String] {
        &self.tags
    }

    /// Get the tag logic
    pub fn tag_logic(&self) -> &TagLogic {
        &self.tag_logic
    }

    /// Get the allowed artwork types
    pub fn artwork_types(&self) -> &[ArtworkType] {
        &self.artwork_types
    }

    /// Check if R18 content is included
    pub fn includes_r18(&self) -> bool {
        self.include_r18
    }

    /// Count how many artworks would pass the filter
    pub fn count_matching_artworks(&self, artworks: &[Artwork]) -> usize {
        artworks
            .iter()
            .filter(|artwork| self.matches_artwork(artwork))
            .count()
    }

    /// Count how many novels would pass the filter
    pub fn count_matching_novels(&self, novels: &[Novel]) -> usize {
        novels
            .iter()
            .filter(|novel| self.matches_novel(novel))
            .count()
    }

    /// Get statistics about filtering results
    pub fn get_filter_stats(&self, artworks: &[Artwork], novels: &[Novel]) -> FilterStats {
        let matching_artworks = self.count_matching_artworks(artworks);
        let matching_novels = self.count_matching_novels(novels);

        FilterStats {
            total_artworks: artworks.len(),
            matching_artworks,
            filtered_artworks: artworks.len() - matching_artworks,
            total_novels: novels.len(),
            matching_novels,
            filtered_novels: novels.len() - matching_novels,
        }
    }

    /// Update the filter with new tags
    pub fn with_tags(mut self, tags: Vec<String>) -> Self {
        self.tags = tags.into_iter().map(|tag| tag.to_lowercase()).collect();
        self
    }

    /// Update the filter with new tag logic
    pub fn with_tag_logic(mut self, tag_logic: TagLogic) -> Self {
        self.tag_logic = tag_logic;
        self
    }

    /// Update the filter with new artwork types
    pub fn with_artwork_types(mut self, artwork_types: Vec<ArtworkType>) -> Self {
        self.artwork_types = artwork_types;
        self
    }

    /// Update the filter to include/exclude R18 content
    pub fn with_r18(mut self, include_r18: bool) -> Self {
        self.include_r18 = include_r18;
        self
    }
}

/// Statistics about filtering results
#[derive(Debug, Clone)]
pub struct FilterStats {
    pub total_artworks: usize,
    pub matching_artworks: usize,
    pub filtered_artworks: usize,
    pub total_novels: usize,
    pub matching_novels: usize,
    pub filtered_novels: usize,
}

impl FilterStats {
    /// Get total matching items
    pub fn total_matching(&self) -> usize {
        self.matching_artworks + self.matching_novels
    }

    /// Get total items
    pub fn total_items(&self) -> usize {
        self.total_artworks + self.total_novels
    }

    /// Get total filtered items
    pub fn total_filtered(&self) -> usize {
        self.filtered_artworks + self.filtered_novels
    }

    /// Get artwork match percentage
    pub fn artwork_match_percentage(&self) -> f64 {
        if self.total_artworks == 0 {
            0.0
        } else {
            (self.matching_artworks as f64 / self.total_artworks as f64) * 100.0
        }
    }

    /// Get novel match percentage
    pub fn novel_match_percentage(&self) -> f64 {
        if self.total_novels == 0 {
            0.0
        } else {
            (self.matching_novels as f64 / self.total_novels as f64) * 100.0
        }
    }

    /// Get overall match percentage
    pub fn overall_match_percentage(&self) -> f64 {
        let total = self.total_items();
        if total == 0 {
            0.0
        } else {
            (self.total_matching() as f64 / total as f64) * 100.0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::api::models::{Tag, User};
    use chrono::{TimeZone, Utc};
    use std::collections::HashMap;

    fn create_test_artwork(
        id: u64,
        tags: Vec<&str>,
        artwork_type: ArtworkType,
        x_restrict: u32,
    ) -> Artwork {
        Artwork {
            id,
            title: format!("Test Artwork {}", id),
            artwork_type,
            image_urls: None,
            caption: "Test caption".to_string(),
            restrict: 0,
            user: User {
                id: 1,
                name: "Test User".to_string(),
                account: "testuser".to_string(),
                profile_image_urls: HashMap::new(),
                comment: None,
                is_followed: false,
            },
            tags: tags
                .into_iter()
                .map(|name| Tag {
                    name: name.to_string(),
                    translated_name: None,
                })
                .collect(),
            tools: vec![],
            create_date: Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap(),
            upload_date: Utc.with_ymd_and_hms(2023, 1, 1, 0, 0, 0).unwrap(),
            page_count: 1,
            width: Some(1920),
            height: Some(1080),
            sanity_level: 2,
            x_restrict,
            series: None,
            meta_single_page: None,
            meta_pages: None,
            total_view: 1000,
            total_bookmarks: 50,
            is_bookmarked: false,
            visible: true,
            is_muted: false,
            total_comments: Some(10),
        }
    }

    #[test]
    fn test_filter_by_tags_or_logic() {
        let artworks = vec![
            create_test_artwork(1, vec!["tag1", "tag2"], ArtworkType::Illust, 0),
            create_test_artwork(2, vec!["tag2", "tag3"], ArtworkType::Illust, 0),
            create_test_artwork(3, vec!["tag4", "tag5"], ArtworkType::Illust, 0),
        ];

        let filter = ContentFilter::new(
            vec!["tag1".to_string(), "tag3".to_string()],
            TagLogic::Or,
            vec![ArtworkType::Illust],
            true,
        );

        let filtered = filter.filter_artworks(&artworks);
        assert_eq!(filtered.len(), 2); // Artworks 1 and 2 should match
        assert_eq!(filtered[0].id, 1);
        assert_eq!(filtered[1].id, 2);
    }

    #[test]
    fn test_filter_by_tags_and_logic() {
        let artworks = vec![
            create_test_artwork(1, vec!["tag1", "tag2"], ArtworkType::Illust, 0),
            create_test_artwork(2, vec!["tag1", "tag3"], ArtworkType::Illust, 0),
            create_test_artwork(3, vec!["tag1", "tag2", "tag3"], ArtworkType::Illust, 0),
        ];

        let filter = ContentFilter::new(
            vec!["tag1".to_string(), "tag2".to_string()],
            TagLogic::And,
            vec![ArtworkType::Illust],
            true,
        );

        let filtered = filter.filter_artworks(&artworks);
        assert_eq!(filtered.len(), 2); // Artworks 1 and 3 should match
        assert_eq!(filtered[0].id, 1);
        assert_eq!(filtered[1].id, 3);
    }

    #[test]
    fn test_filter_by_artwork_type() {
        let artworks = vec![
            create_test_artwork(1, vec!["tag1"], ArtworkType::Illust, 0),
            create_test_artwork(2, vec!["tag1"], ArtworkType::Manga, 0),
        ];

        let filter = ContentFilter::new(vec![], TagLogic::Or, vec![ArtworkType::Illust], true);

        let filtered = filter.filter_artworks(&artworks);
        assert_eq!(filtered.len(), 1); // Only artwork 1 should match
        assert_eq!(filtered[0].id, 1);
    }

    #[test]
    fn test_filter_r18_content() {
        let artworks = vec![
            create_test_artwork(1, vec!["tag1"], ArtworkType::Illust, 0), // Not R18
            create_test_artwork(2, vec!["tag1"], ArtworkType::Illust, 1), // R18
        ];

        let filter = ContentFilter::new(
            vec![],
            TagLogic::Or,
            vec![ArtworkType::Illust],
            false, // Don't include R18
        );

        let filtered = filter.filter_artworks(&artworks);
        assert_eq!(filtered.len(), 1); // Only artwork 1 should match
        assert_eq!(filtered[0].id, 1);
    }

    #[test]
    fn test_accept_all_filter() {
        let artworks = vec![
            create_test_artwork(1, vec!["tag1"], ArtworkType::Illust, 0),
            create_test_artwork(2, vec!["tag2"], ArtworkType::Manga, 1),
        ];

        let filter = ContentFilter::accept_all(vec![ArtworkType::Illust, ArtworkType::Manga]);
        let filtered = filter.filter_artworks(&artworks);
        assert_eq!(filtered.len(), 2); // Both artworks should match
    }

    #[test]
    fn test_filter_stats() {
        let artworks = vec![
            create_test_artwork(1, vec!["tag1"], ArtworkType::Illust, 0),
            create_test_artwork(2, vec!["tag2"], ArtworkType::Manga, 0),
        ];

        let filter = ContentFilter::new(
            vec!["tag1".to_string()],
            TagLogic::Or,
            vec![ArtworkType::Illust, ArtworkType::Manga],
            true,
        );

        let stats = filter.get_filter_stats(&artworks, &[]);
        assert_eq!(stats.total_artworks, 2);
        assert_eq!(stats.matching_artworks, 1);
        assert_eq!(stats.filtered_artworks, 1);
        assert_eq!(stats.artwork_match_percentage(), 50.0);
    }
}
