use crate::api::models::ArtworkType;
use crate::api::{
    client::PixivClient,
    endpoints::PixivApi,
    models::{Artwork, Novel},
};
use crate::cli::interactive::{DownloadStats, InteractiveInterface, TagSelectionMethod};
use crate::config::settings::{Config, TagLogic};
use crate::core::filter::ContentFilter;
use crate::downloader::direct::DirectDownloader;
use crate::storage::{metadata::MetadataFormatter, path_template::PathTemplate};
use crate::utils::error::Result;
use crate::utils::helpers::ensure_directory_exists;
use chrono::Utc;

use tracing::{error, info, warn};

/// Main workflow orchestrator
pub struct WorkflowManager {
    config: Config,
    api: PixivApi,
    downloader: DirectDownloader,
    metadata_formatter: MetadataFormatter,
    interactive: InteractiveInterface,
}

impl WorkflowManager {
    /// Create a new workflow manager
    pub async fn new(config: Config) -> Result<Self> {
        // Create API client
        let client = PixivClient::new(&config).await?;
        let api = PixivApi::new(client.clone());

        // Create downloader
        let downloader = DirectDownloader::new(client, true);

        // Create metadata formatter
        let metadata_formatter = MetadataFormatter::new_default();

        // Create interactive interface
        let interactive = InteractiveInterface::new();

        Ok(Self {
            config,
            api,
            downloader,
            metadata_formatter,
            interactive,
        })
    }

    /// Run interactive workflow
    pub async fn run_interactive(&self) -> Result<()> {
        info!("Starting interactive workflow");

        // Get user ID
        let user_id = self.interactive.get_user_id()?;

        // Get user profile
        let user = self.api.get_user_profile(user_id).await?;
        info!("User: {} ({})", user.name, user.id);

        // Get artwork types to download
        let artwork_types = self.interactive.get_artwork_types()?;

        // Get tag selection method
        let tag_method = self.interactive.get_tag_selection_method()?;

        let (selected_tags, tag_logic) = match tag_method {
            TagSelectionMethod::All => {
                info!("Downloading all works without tag filtering");
                (vec![], TagLogic::Or)
            }
            TagSelectionMethod::Manual => {
                let tags = self.interactive.get_manual_tags()?;
                let logic = self.interactive.get_tag_logic(&tags)?;
                (tags, logic)
            }
            TagSelectionMethod::FromList => {
                self.interactive.show_tag_extraction_progress();

                // Fetch all works to extract tags
                let mut all_tags = Vec::new();

                if artwork_types.contains(&ArtworkType::Illust)
                    || artwork_types.contains(&ArtworkType::Manga)
                {
                    let artworks = self.api.get_all_user_artworks(user_id).await?;
                    all_tags.extend(self.api.extract_artwork_tags(&artworks));
                }

                if artwork_types.contains(&ArtworkType::Novel) {
                    let novels = self.api.get_all_user_novels(user_id).await?;
                    all_tags.extend(self.api.extract_novel_tags(&novels));
                }

                // Remove duplicates and sort
                all_tags.sort();
                all_tags.dedup();

                if all_tags.is_empty() {
                    self.interactive.show_warning("No tags found for this user");
                    (vec![], TagLogic::Or)
                } else {
                    let tags = self.interactive.select_tags_from_list(&all_tags)?;
                    let logic = self.interactive.get_tag_logic(&tags)?;
                    (tags, logic)
                }
            }
        };

        // Get R18 preference
        let include_r18 = self.interactive.get_r18_preference()?;

        // Create filter
        let filter = ContentFilter::new(
            selected_tags.clone(),
            tag_logic.clone(),
            artwork_types.clone(),
            include_r18,
        );

        // Fetch and filter content
        let (filtered_artworks, filtered_novels) =
            self.fetch_and_filter_content(user_id, &filter).await?;

        // Show download confirmation
        let stats = DownloadStats {
            user_id,
            username: user.name.clone(),
            selected_tags,
            tag_logic,
            artwork_types,
            include_r18,
            artwork_count: filtered_artworks.len(),
            novel_count: filtered_novels.len(),
        };

        if !self.interactive.confirm_download(&stats)? {
            info!("Download cancelled by user");
            return Ok(());
        }

        // Start download
        self.download_content(&user, &filtered_artworks, &filtered_novels)
            .await?;

        info!("Interactive workflow completed successfully");
        Ok(())
    }

    /// Run command line workflow
    pub async fn run_command_line(
        &self,
        user_id: u64,
        tags: Vec<String>,
        tag_logic: TagLogic,
        artwork_types: Vec<ArtworkType>,
        download_all: bool,
    ) -> Result<()> {
        info!("Starting command line workflow for user {}", user_id);

        // Get user profile
        let user = self.api.get_user_profile(user_id).await?;
        info!("User: {} ({})", user.name, user.id);

        // Create filter
        let filter = if download_all {
            ContentFilter::accept_all(artwork_types)
        } else {
            ContentFilter::new(tags, tag_logic, artwork_types, true) // TODO: Make R18 configurable
        };

        // Fetch and filter content
        let (filtered_artworks, filtered_novels) =
            self.fetch_and_filter_content(user_id, &filter).await?;

        info!(
            "Found {} artworks and {} novels to download",
            filtered_artworks.len(),
            filtered_novels.len()
        );

        if filtered_artworks.is_empty() && filtered_novels.is_empty() {
            warn!("No content matches the specified criteria");
            return Ok(());
        }

        // Start download
        self.download_content(&user, &filtered_artworks, &filtered_novels)
            .await?;

        info!("Command line workflow completed successfully");
        Ok(())
    }

    /// Fetch and filter content based on the filter criteria
    async fn fetch_and_filter_content(
        &self,
        user_id: u64,
        filter: &ContentFilter,
    ) -> Result<(Vec<Artwork>, Vec<Novel>)> {
        let mut artworks = Vec::new();
        let mut novels = Vec::new();

        // Fetch artworks if needed
        if filter.artwork_types().contains(&ArtworkType::Illust)
            || filter.artwork_types().contains(&ArtworkType::Manga)
        {
            info!("Fetching user artworks...");
            let all_artworks = self.api.get_all_user_artworks(user_id).await?;
            artworks = filter
                .filter_artworks(&all_artworks)
                .into_iter()
                .cloned()
                .collect();
            info!(
                "Filtered {} artworks from {} total",
                artworks.len(),
                all_artworks.len()
            );
        }

        // Fetch novels if needed
        if filter.artwork_types().contains(&ArtworkType::Novel) {
            info!("Fetching user novels...");
            let all_novels = self.api.get_all_user_novels(user_id).await?;
            novels = filter
                .filter_novels(&all_novels)
                .into_iter()
                .cloned()
                .collect();
            info!(
                "Filtered {} novels from {} total",
                novels.len(),
                all_novels.len()
            );
        }

        Ok((artworks, novels))
    }

    /// Download all filtered content
    async fn download_content(
        &self,
        _user: &crate::api::models::User,
        artworks: &[Artwork],
        novels: &[Novel],
    ) -> Result<()> {
        let download_time = Utc::now();

        // Ensure output directory exists
        ensure_directory_exists(&self.config.output_dir)?;

        // Download artworks
        if !artworks.is_empty() {
            info!("Downloading {} artworks...", artworks.len());
            for artwork in artworks {
                if let Err(e) = self.download_artwork(artwork, download_time).await {
                    error!("Failed to download artwork {}: {}", artwork.id, e);
                    // Continue with next artwork instead of failing completely
                }

                // Add delay between downloads
                self.api
                    .client()
                    .delay(self.config.delay_range.0, self.config.delay_range.1)
                    .await;
            }
        }

        // Download novels
        if !novels.is_empty() {
            info!("Downloading {} novels...", novels.len());
            for novel in novels {
                if let Err(e) = self.download_novel(novel, download_time).await {
                    error!("Failed to download novel {}: {}", novel.id, e);
                    // Continue with next novel instead of failing completely
                }

                // Add delay between downloads
                self.api
                    .client()
                    .delay(self.config.delay_range.0, self.config.delay_range.1)
                    .await;
            }
        }

        Ok(())
    }

    /// Download a single artwork with all its pages
    async fn download_artwork(
        &self,
        artwork: &Artwork,
        download_time: chrono::DateTime<Utc>,
    ) -> Result<()> {
        info!("Downloading artwork: {} ({})", artwork.title, artwork.id);

        // Get full artwork details with pages
        let (full_artwork, pages) = self.api.get_full_artwork_details(artwork.id_as_u64()).await?;

        // Create path template for directory
        let dir_template = PathTemplate::new(
            self.config.path_templates.directory.clone(),
            self.config.max_filename_length,
            self.config.filename_replacement_char.clone(),
        );

        // Create path template for filename
        let file_template = PathTemplate::new(
            self.config.path_templates.filename.clone(),
            self.config.max_filename_length,
            self.config.filename_replacement_char.clone(),
        );

        // Download each page
        for (page_index, page) in pages.iter().enumerate() {
            // Determine file extension
            let url = &page.image_urls.original;
            let extension = url.split('.').next_back().unwrap_or("jpg");

            // Generate file path
            let filename =
                file_template.resolve_for_artwork(&full_artwork, Some(page_index), extension)?;
            let dir_path = dir_template.resolve_for_artwork(&full_artwork, None, "")?;
            let full_dir_path = self.config.output_dir.join(dir_path);
            let full_file_path = full_dir_path.join(filename);

            // Check if file already exists based on conflict strategy
            if full_file_path.exists() {
                match self.config.conflict_strategy {
                    crate::config::settings::ConflictStrategy::Skip => {
                        info!("Skipping existing file: {}", full_file_path.display());
                        continue;
                    }
                    crate::config::settings::ConflictStrategy::Overwrite => {
                        info!("Overwriting existing file: {}", full_file_path.display());
                    }
                    crate::config::settings::ConflictStrategy::Rename => {
                        let unique_path =
                            crate::utils::helpers::generate_unique_filename(&full_file_path);
                        info!("Renaming to avoid conflict: {}", unique_path.display());
                        // Use the unique path for download
                        // TODO: Update full_file_path to unique_path
                    }
                }
            }

            // Download the image
            self.downloader
                .download_file_with_retry(
                    url,
                    &full_file_path,
                    None,
                    self.config.retry_count,
                    self.config.retry_delay,
                )
                .await?;
        }

        // Save metadata
        let metadata_filename = if pages.len() > 1 {
            // Multi-page artwork: save metadata in the artwork directory
            let dir_path = dir_template.resolve_for_artwork(&full_artwork, None, "")?;
            let full_dir_path = self.config.output_dir.join(dir_path);
            full_dir_path.join(&self.config.path_templates.metadata_filename)
        } else {
            // Single-page artwork: save metadata next to the image
            let filename = file_template.resolve_for_artwork(&full_artwork, Some(0), "txt")?;
            let dir_path = dir_template.resolve_for_artwork(&full_artwork, None, "")?;
            let full_dir_path = self.config.output_dir.join(dir_path);
            full_dir_path.join(filename)
        };

        self.metadata_formatter
            .save_artwork_metadata(metadata_filename, &full_artwork, &pages, download_time)
            .await?;

        info!("Successfully downloaded artwork: {}", artwork.id);
        Ok(())
    }

    /// Download a single novel
    async fn download_novel(
        &self,
        novel: &Novel,
        download_time: chrono::DateTime<Utc>,
    ) -> Result<()> {
        info!("Downloading novel: {} ({})", novel.title, novel.id);

        // Get full novel details with content
        let full_novel = self.api.get_full_novel_details(novel.id).await?;

        // Create path templates
        let dir_template = PathTemplate::new(
            self.config.path_templates.directory.clone(),
            self.config.max_filename_length,
            self.config.filename_replacement_char.clone(),
        );

        let file_template = PathTemplate::new(
            self.config.path_templates.filename.clone(),
            self.config.max_filename_length,
            self.config.filename_replacement_char.clone(),
        );

        // Generate file path
        let filename = file_template.resolve_for_novel(&full_novel, "txt")?;
        let dir_path = dir_template.resolve_for_novel(&full_novel, "")?;
        let full_dir_path = self.config.output_dir.join(dir_path);
        let full_file_path = full_dir_path.join(filename);

        // Check if file already exists
        if full_file_path.exists() {
            match self.config.conflict_strategy {
                crate::config::settings::ConflictStrategy::Skip => {
                    info!("Skipping existing novel: {}", full_file_path.display());
                    return Ok(());
                }
                crate::config::settings::ConflictStrategy::Overwrite => {
                    info!("Overwriting existing novel: {}", full_file_path.display());
                }
                crate::config::settings::ConflictStrategy::Rename => {
                    let unique_path =
                        crate::utils::helpers::generate_unique_filename(&full_file_path);
                    info!("Renaming to avoid conflict: {}", unique_path.display());
                    // TODO: Use unique_path for saving
                }
            }
        }

        // Save novel content with metadata
        self.metadata_formatter
            .save_novel_content(full_file_path, &full_novel, download_time)
            .await?;

        info!("Successfully downloaded novel: {}", novel.id);
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    // Note: These tests would require mock implementations
    // For now, we'll just test basic structure

    #[test]
    fn test_workflow_manager_structure() {
        // This test just ensures the struct can be constructed
        // Real tests would require mocking the dependencies
        assert!(true);
    }
}
