use crate::auth::cookie::<PERSON><PERSON>Manager;
use crate::config::settings::Config;
use crate::utils::error::{Error, Result};
use reqwest::{Client, ClientBuilder};
use std::time::Duration;
use tracing::{debug, info};

/// Pixiv API client
#[derive(Debug, Clone)]
pub struct PixivClient {
    client: Client,
    cookie_manager: <PERSON>ieManager,
    base_url: String,
}

impl PixivClient {
    /// Create a new Pixiv API client
    pub async fn new(config: &Config) -> Result<Self> {
        // Load cookies
        let cookie_manager = CookieManager::from_file(&config.cookie_file)?;
        cookie_manager.validate()?;

        // Build HTTP client
        let mut client_builder = ClientBuilder::new()
            .timeout(Duration::from_secs(config.timeout))
            .user_agent(&config.http_headers.user_agent);

        // Add default headers
        let mut default_headers = reqwest::header::HeaderMap::new();

        // Add referer
        default_headers.insert(
            reqwest::header::REFERER,
            config
                .http_headers
                .referer
                .parse()
                .map_err(|e| Error::config(format!("Invalid referer header: {}", e)))?,
        );

        // Add accept-language
        default_headers.insert(
            reqwest::header::ACCEPT_LANGUAGE,
            config
                .http_headers
                .accept_language
                .parse()
                .map_err(|e| Error::config(format!("Invalid accept-language header: {}", e)))?,
        );

        // Add custom headers
        for (key, value) in &config.http_headers.custom_headers {
            let header_name: reqwest::header::HeaderName = key
                .parse()
                .map_err(|e| Error::config(format!("Invalid header name '{}': {}", key, e)))?;
            let header_value: reqwest::header::HeaderValue = value
                .parse()
                .map_err(|e| Error::config(format!("Invalid header value for '{}': {}", key, e)))?;
            default_headers.insert(header_name, header_value);
        }

        client_builder = client_builder.default_headers(default_headers);

        let client = client_builder
            .build()
            .map_err(|e| Error::network(format!("Failed to build HTTP client: {}", e)))?;

        let pixiv_client = Self {
            client,
            cookie_manager,
            base_url: "https://www.pixiv.net".to_string(),
        };

        // Test authentication
        info!("Testing authentication...");
        let auth_valid = pixiv_client.test_authentication().await?;
        if !auth_valid {
            return Err(Error::auth(
                "Authentication test failed. Please check your cookies.".to_string(),
            ));
        }
        info!("Authentication successful");

        Ok(pixiv_client)
    }

    /// Test authentication by making a simple API call
    pub async fn test_authentication(&self) -> Result<bool> {
        self.cookie_manager.test_authentication(&self.client).await
    }

    /// Get the HTTP client
    pub fn client(&self) -> &Client {
        &self.client
    }

    /// Get the cookie manager
    pub fn cookie_manager(&self) -> &CookieManager {
        &self.cookie_manager
    }

    /// Make an authenticated GET request
    pub async fn get(&self, url: &str) -> Result<reqwest::Response> {
        debug!("Making GET request to: {}", url);

        let headers = self.cookie_manager.to_header_map()?;

        let response = self
            .client
            .get(url)
            .headers(headers)
            .send()
            .await
            .map_err(|e| Error::network(format!("GET request failed: {}", e)))?;

        if !response.status().is_success() {
            return Err(Error::api(format!(
                "API request failed with status: {}",
                response.status()
            )));
        }

        Ok(response)
    }

    /// Make an authenticated GET request without checking status code
    pub async fn get_raw(&self, url: &str) -> Result<reqwest::Response> {
        debug!("Making GET request to: {}", url);

        let headers = self.cookie_manager.to_header_map()?;

        let response = self
            .client
            .get(url)
            .headers(headers)
            .send()
            .await
            .map_err(|e| Error::network(format!("GET request failed: {}", e)))?;

        Ok(response)
    }

    /// Make an authenticated POST request
    pub async fn post(&self, url: &str, body: Option<&str>) -> Result<reqwest::Response> {
        debug!("Making POST request to: {}", url);

        let headers = self.cookie_manager.to_header_map()?;

        let mut request = self.client.post(url).headers(headers);

        if let Some(body_content) = body {
            request = request.body(body_content.to_string());
        }

        let response = request
            .send()
            .await
            .map_err(|e| Error::network(format!("POST request failed: {}", e)))?;

        if !response.status().is_success() {
            return Err(Error::api(format!(
                "API request failed with status: {}",
                response.status()
            )));
        }

        Ok(response)
    }

    /// Build a full URL from a relative path
    pub fn build_url(&self, path: &str) -> String {
        if path.starts_with("http") {
            path.to_string()
        } else {
            format!("{}{}", self.base_url, path)
        }
    }

    /// Get JSON response from an API endpoint
    pub async fn get_json<T>(&self, url: &str) -> Result<T>
    where
        T: serde::de::DeserializeOwned,
    {
        let response = self.get(url).await?;
        let json = response
            .json::<T>()
            .await
            .map_err(|e| Error::api(format!("Failed to parse JSON response: {}", e)))?;
        Ok(json)
    }

    /// Download a file from a URL
    pub async fn download_file(&self, url: &str) -> Result<reqwest::Response> {
        debug!("Downloading file from: {}", url);

        let headers = self.cookie_manager.to_header_map()?;

        let response = self
            .client
            .get(url)
            .headers(headers)
            .send()
            .await
            .map_err(|e| Error::download(format!("File download failed: {}", e)))?;

        if !response.status().is_success() {
            return Err(Error::download(format!(
                "File download failed with status: {}",
                response.status()
            )));
        }

        Ok(response)
    }

    /// Get the base URL
    pub fn base_url(&self) -> &str {
        &self.base_url
    }

    /// Check if the client is authenticated
    pub async fn is_authenticated(&self) -> bool {
        self.test_authentication().await.unwrap_or(false)
    }

    /// Get user ID from cookies if available
    pub fn get_user_id(&self) -> Option<u64> {
        self.cookie_manager.get_user_id()
    }

    /// Add delay between requests to avoid rate limiting
    pub async fn delay(&self, min_seconds: u64, max_seconds: u64) {
        use rand::Rng;

        let delay_seconds = if min_seconds == max_seconds {
            min_seconds
        } else {
            rand::thread_rng().gen_range(min_seconds..=max_seconds)
        };

        debug!("Delaying for {} seconds", delay_seconds);
        tokio::time::sleep(Duration::from_secs(delay_seconds)).await;
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::settings::HttpHeaders;
    use std::collections::HashMap;

    fn create_test_config() -> Config {
        Config {
            cookie_file: std::path::PathBuf::from("test_cookie.txt"),
            timeout: 30,
            http_headers: HttpHeaders {
                user_agent: "test-agent".to_string(),
                referer: "https://www.pixiv.net/".to_string(),
                accept_language: "en-US".to_string(),
                custom_headers: HashMap::new(),
            },
            ..Default::default()
        }
    }

    #[test]
    fn test_build_url() {
        let config = create_test_config();
        // We can't actually create a client without valid cookies, so we'll test URL building logic separately
        let base_url = "https://www.pixiv.net";

        // Test relative path
        let relative_path = "/ajax/user/12345";
        let full_url = format!("{}{}", base_url, relative_path);
        assert_eq!(full_url, "https://www.pixiv.net/ajax/user/12345");

        // Test absolute URL
        let absolute_url = "https://example.com/test";
        assert_eq!(absolute_url, "https://example.com/test");
    }

    #[test]
    fn test_config_validation() {
        let config = create_test_config();

        // Test that config has required fields
        assert!(!config.http_headers.user_agent.is_empty());
        assert!(!config.http_headers.referer.is_empty());
        assert!(config.timeout > 0);
    }
}
