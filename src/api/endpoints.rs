use crate::api::client::PixivClient;
use crate::api::models::*;
use crate::utils::error::{Error, Result};
use tracing::{debug, info, warn};

/// Pixiv API endpoints
pub struct PixivApi {
    client: PixivClient,
}

impl PixivApi {
    /// Create a new API instance
    pub fn new(client: PixivClient) -> Self {
        Self { client }
    }

    /// Get user profile information
    pub async fn get_user_profile(&self, user_id: u64) -> Result<User> {
        let url = self.client.build_url(&format!("/ajax/user/{}", user_id));

        let response: ApiResponse<UserProfileBody> = self.client.get_json(&url).await?;

        if response.error {
            return Err(Error::api(format!("API error: {}", response.message)));
        }

        let body = response
            .body
            .ok_or_else(|| Error::api("Empty response body".to_string()))?;
        Ok(body.to_user())
    }

    /// Get user's artwork IDs (illustrations and manga)
    pub async fn get_user_artwork_ids(&self, user_id: u64) -> Result<Vec<u64>> {
        let url = self.client.build_url(&format!("/ajax/user/{}/profile/all", user_id));

        // First get the raw response to debug the structure
        let raw_response = self.client.get(&url).await?;
        let response_text = raw_response.text().await.map_err(|e| {
            Error::api(format!("Failed to get response text: {}", e))
        })?;

        // Log the response for debugging
        debug!("Raw user artworks response for {}: {}", user_id, response_text);

        // Try to parse as JSON
        let response: ApiResponse<serde_json::Value> = serde_json::from_str(&response_text)
            .map_err(|e| Error::api(format!("Failed to parse JSON response: {}", e)))?;

        if response.error {
            return Err(Error::api(format!("API error: {}", response.message)));
        }

        let body = response
            .body
            .ok_or_else(|| Error::api("Empty response body".to_string()))?;

        // Try to parse the user artworks body
        let user_artworks: UserArtworksBody = serde_json::from_value(body.clone())
            .map_err(|e| {
                warn!("Failed to parse user artworks for {}: {}", user_id, e);
                warn!("User artworks JSON: {}", serde_json::to_string_pretty(&body).unwrap_or_default());
                Error::api(format!("Failed to parse user artworks: {}", e))
            })?;

        Ok(user_artworks.get_artwork_ids())
    }

    /// Get user's artworks (illustrations and manga) with full details
    pub async fn get_user_artworks(
        &self,
        user_id: u64,
        offset: u32,
        limit: u32,
    ) -> Result<Vec<Artwork>> {
        // First get all artwork IDs
        let artwork_ids = self.get_user_artwork_ids(user_id).await?;

        // Apply pagination
        let start = offset as usize;
        let end = std::cmp::min(start + limit as usize, artwork_ids.len());

        if start >= artwork_ids.len() {
            return Ok(Vec::new());
        }

        let page_ids = &artwork_ids[start..end];
        let mut artworks = Vec::new();

        // Fetch details for each artwork
        for &artwork_id in page_ids {
            match self.get_artwork_detail(artwork_id).await {
                Ok(artwork) => artworks.push(artwork),
                Err(e) => {
                    warn!("Failed to fetch artwork {}: {}", artwork_id, e);
                    // Continue with other artworks instead of failing completely
                }
            }

            // Add delay between requests to avoid rate limiting
            self.client.delay(1, 2).await;
        }

        Ok(artworks)
    }

    /// Get user's novels
    pub async fn get_user_novels(
        &self,
        user_id: u64,
        offset: u32,
        limit: u32,
    ) -> Result<Vec<Novel>> {
        let url = self.client.build_url(&format!(
            "/ajax/user/{}/profile/novels?offset={}&limit={}",
            user_id, offset, limit
        ));

        // Try to get the response, but handle 400 errors gracefully
        let raw_response = self.client.get_raw(&url).await?;
        let status = raw_response.status();

        if status == 400 {
            // 400 Bad Request likely means the user has no novels or the endpoint doesn't exist for this user
            warn!("User {} has no novels or novels endpoint not available", user_id);
            return Ok(Vec::new());
        }

        let response_text = raw_response.text().await.map_err(|e| {
            Error::api(format!("Failed to get response text: {}", e))
        })?;

        let response: ApiResponse<UserNovelsBody> = serde_json::from_str(&response_text)
            .map_err(|e| Error::api(format!("Failed to parse JSON response: {}", e)))?;

        if response.error {
            return Err(Error::api(format!("API error: {}", response.message)));
        }

        let body = response
            .body
            .ok_or_else(|| Error::api("Empty response body".to_string()))?;
        Ok(body.works)
    }

    /// Get detailed artwork information
    pub async fn get_artwork_detail(&self, artwork_id: u64) -> Result<Artwork> {
        let url = self
            .client
            .build_url(&format!("/ajax/illust/{}", artwork_id));

        // First get the raw response to debug the structure
        let raw_response = self.client.get(&url).await?;
        let response_text = raw_response.text().await.map_err(|e| {
            Error::api(format!("Failed to get response text: {}", e))
        })?;

        // Log the response for debugging
        debug!("Raw artwork detail response for {}: {}", artwork_id, response_text);

        // Try to parse as JSON
        let response: ApiResponse<serde_json::Value> = serde_json::from_str(&response_text)
            .map_err(|e| Error::api(format!("Failed to parse JSON response: {}", e)))?;

        if response.error {
            return Err(Error::api(format!("API error: {}", response.message)));
        }

        let body = response
            .body
            .ok_or_else(|| Error::api("Empty response body".to_string()))?;

        // Try to parse the artwork from the body directly
        // The artwork data is directly in the body, not in an 'illust' field
        let artwork: Artwork = serde_json::from_value(body.clone())
            .map_err(|e| {
                warn!("Failed to parse artwork {}: {}", artwork_id, e);
                warn!("Artwork JSON: {}", serde_json::to_string_pretty(&body).unwrap_or_default());
                Error::api(format!("Failed to parse artwork: {}", e))
            })?;

        Ok(artwork)
    }

    /// Get detailed novel information
    pub async fn get_novel_detail(&self, novel_id: u64) -> Result<Novel> {
        let url = self.client.build_url(&format!("/ajax/novel/{}", novel_id));

        let response: ApiResponse<NovelDetailBody> = self.client.get_json(&url).await?;

        if response.error {
            return Err(Error::api(format!("API error: {}", response.message)));
        }

        let body = response
            .body
            .ok_or_else(|| Error::api("Empty response body".to_string()))?;
        Ok(body.novel)
    }

    /// Get novel content (text)
    pub async fn get_novel_content(&self, novel_id: u64) -> Result<String> {
        let url = self
            .client
            .build_url(&format!("/ajax/novel/{}/content", novel_id));

        let response: ApiResponse<NovelContentBody> = self.client.get_json(&url).await?;

        if response.error {
            return Err(Error::api(format!("API error: {}", response.message)));
        }

        let body = response
            .body
            .ok_or_else(|| Error::api("Empty response body".to_string()))?;
        Ok(body.novel_text)
    }

    /// Get pages for multi-page artwork
    pub async fn get_artwork_pages(&self, artwork_id: u64) -> Result<Vec<PageDetail>> {
        let url = self
            .client
            .build_url(&format!("/ajax/illust/{}/pages", artwork_id));

        let response: ApiResponse<ArtworkPagesBody> = self.client.get_json(&url).await?;

        if response.error {
            return Err(Error::api(format!("API error: {}", response.message)));
        }

        let body = response
            .body
            .ok_or_else(|| Error::api("Empty response body".to_string()))?;
        Ok(body.pages)
    }

    /// Get all artworks for a user with pagination
    pub async fn get_all_user_artworks(&self, user_id: u64) -> Result<Vec<Artwork>> {
        let mut all_artworks = Vec::new();
        let mut offset = 0;
        let limit = 48; // Pixiv's default page size

        loop {
            info!(
                "Fetching artworks for user {} (offset: {})",
                user_id, offset
            );

            let artworks = self.get_user_artworks(user_id, offset, limit).await?;

            if artworks.is_empty() {
                break;
            }

            let count = artworks.len();
            all_artworks.extend(artworks);
            offset += count as u32;

            // Add delay between requests
            self.client.delay(1, 2).await;

            // If we got fewer results than the limit, we've reached the end
            if count < limit as usize {
                break;
            }
        }

        info!(
            "Retrieved {} total artworks for user {}",
            all_artworks.len(),
            user_id
        );
        Ok(all_artworks)
    }

    /// Get all novels for a user with pagination
    pub async fn get_all_user_novels(&self, user_id: u64) -> Result<Vec<Novel>> {
        let mut all_novels = Vec::new();
        let mut offset = 0;
        let limit = 24; // Pixiv's default page size for novels

        loop {
            info!("Fetching novels for user {} (offset: {})", user_id, offset);

            let novels = self.get_user_novels(user_id, offset, limit).await?;

            if novels.is_empty() {
                break;
            }

            let count = novels.len();
            all_novels.extend(novels);
            offset += count as u32;

            // Add delay between requests
            self.client.delay(1, 2).await;

            // If we got fewer results than the limit, we've reached the end
            if count < limit as usize {
                break;
            }
        }

        info!(
            "Retrieved {} total novels for user {}",
            all_novels.len(),
            user_id
        );
        Ok(all_novels)
    }

    /// Get artwork with full details including pages
    pub async fn get_full_artwork_details(
        &self,
        artwork_id: u64,
    ) -> Result<(Artwork, Vec<PageDetail>)> {
        debug!("Getting full details for artwork {}", artwork_id);

        let artwork = self.get_artwork_detail(artwork_id).await?;

        let pages = if artwork.page_count.unwrap_or(1) > 1 {
            self.get_artwork_pages(artwork_id).await?
        } else {
            // For single-page artworks, create a single page detail from the artwork
            if let Some(urls) = &artwork.urls {
                // Create ImageUrls from the urls map
                let image_urls = ImageUrls {
                    square_medium: urls.get("mini").cloned(),
                    medium: urls.get("thumb").cloned(),
                    large: urls.get("regular").cloned(),
                    original: urls.get("original").cloned().unwrap_or_default(),
                };
                vec![PageDetail {
                    image_urls,
                    width: artwork.width.unwrap_or(0),
                    height: artwork.height.unwrap_or(0),
                }]
            } else {
                vec![]
            }
        };

        // Add delay after fetching details
        self.client.delay(1, 2).await;

        Ok((artwork, pages))
    }

    /// Get novel with full content
    pub async fn get_full_novel_details(&self, novel_id: u64) -> Result<Novel> {
        debug!("Getting full details for novel {}", novel_id);

        let mut novel = self.get_novel_detail(novel_id).await?;

        // Fetch the novel content
        match self.get_novel_content(novel_id).await {
            Ok(content) => {
                novel.content = Some(content);
            }
            Err(e) => {
                warn!("Failed to fetch content for novel {}: {}", novel_id, e);
                // Continue without content rather than failing completely
            }
        }

        // Add delay after fetching details
        self.client.delay(1, 2).await;

        Ok(novel)
    }

    /// Extract all unique tags from a list of artworks
    pub fn extract_artwork_tags(&self, artworks: &[Artwork]) -> Vec<String> {
        let mut tags = std::collections::HashSet::new();

        for artwork in artworks {
            let artwork_tags = artwork.tag_names();
            for tag in artwork_tags {
                tags.insert(tag);
            }
        }

        let mut tag_list: Vec<String> = tags.into_iter().collect();
        tag_list.sort();
        tag_list
    }

    /// Extract all unique tags from a list of novels
    pub fn extract_novel_tags(&self, novels: &[Novel]) -> Vec<String> {
        let mut tags = std::collections::HashSet::new();

        for novel in novels {
            for tag in &novel.tags {
                tags.insert(tag.name.clone());
            }
        }

        let mut tag_list: Vec<String> = tags.into_iter().collect();
        tag_list.sort();
        tag_list
    }

    /// Get the underlying client
    pub fn client(&self) -> &PixivClient {
        &self.client
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_extract_artwork_tags() {
        // This test would require mock data, but demonstrates the concept
        let artworks: Vec<Artwork> = vec![];
        // Skip the actual API creation for now since it requires a real client
        // let api = PixivApi::new(mock_client);
        // let tags = api.extract_artwork_tags(&artworks);
        // For now, just test that empty artworks return empty tags
        assert!(artworks.is_empty());
    }
}
