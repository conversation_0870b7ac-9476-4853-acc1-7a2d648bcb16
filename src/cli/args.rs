use crate::api::models::ArtworkType;
use crate::config::settings::DownloadMethod;
use clap::{Parser, ValueEnum};
use std::path::PathBuf;

/// Command line arguments for PixivTagDownloader
#[derive(Parser, Debug)]
#[command(
    name = "PixivTagDownloader",
    version = env!("CARGO_PKG_VERSION"),
    author = "Mannix Sun <<EMAIL>>",
    about = "A Rust application for downloading Pixiv artworks by user ID and tags",
    long_about = "PixivTagDownloader allows you to download artworks from Pixiv based on user IDs and tags. \
                  It supports various download methods including direct download and Aria2 integration."
)]
pub struct Args {
    /// Pixiv user ID to download from
    #[arg(short = 'u', long = "uid", value_name = "UID")]
    pub uid: Option<u64>,

    /// Tags to filter artworks (comma-separated)
    #[arg(short = 't', long = "tags", value_name = "TAGS")]
    pub tags: Option<String>,

    /// Tag filtering logic (and/or)
    #[arg(
        short = 'l',
        long = "logic",
        value_name = "LOGIC",
        default_value = "or"
    )]
    pub logic: Option<String>,

    /// Download all artworks without tag filtering
    #[arg(long = "all")]
    pub download_all: bool,

    /// Artwork types to download (comma-separated)
    #[arg(long = "type", value_name = "TYPES")]
    pub artwork_type: Option<Vec<ArtworkType>>,

    /// Path to cookie.txt file
    #[arg(long = "cookie-file", value_name = "PATH")]
    pub cookie_file: Option<PathBuf>,

    /// Output directory
    #[arg(short = 'o', long = "output-dir", value_name = "PATH")]
    pub output_dir: Option<PathBuf>,

    /// Configuration file path
    #[arg(short = 'c', long = "config", value_name = "PATH")]
    pub config: Option<PathBuf>,

    /// Download method
    #[arg(long = "download-method", value_name = "METHOD")]
    pub download_method: Option<DownloadMethod>,

    /// Aria2 RPC URL
    #[arg(long = "aria2-rpc-url", value_name = "URL")]
    pub aria2_rpc_url: Option<String>,

    /// Aria2 RPC secret token
    #[arg(long = "aria2-rpc-secret", value_name = "TOKEN")]
    pub aria2_rpc_secret: Option<String>,

    /// Number of concurrent downloads
    #[arg(long = "concurrency", value_name = "NUM")]
    pub concurrency: Option<usize>,

    /// Random delay range in seconds (e.g., "1-3")
    #[arg(long = "delay", value_name = "MIN-MAX")]
    pub delay: Option<String>,

    /// Skip existing files
    #[arg(long = "skip-existing")]
    pub skip_existing: bool,

    /// Overwrite existing files
    #[arg(long = "overwrite-existing")]
    pub overwrite_existing: bool,

    /// Rename existing files
    #[arg(long = "rename-existing")]
    pub rename_existing: bool,

    /// Log level
    #[arg(long = "log-level", value_name = "LEVEL", default_value = "info")]
    pub log_level: Option<String>,

    /// Enable interactive mode
    #[arg(short = 'i', long = "interactive")]
    pub interactive: bool,

    /// Dry run mode (don't actually download)
    #[arg(long = "dry-run")]
    pub dry_run: bool,

    /// Verbose output
    #[arg(short = 'v', long = "verbose")]
    pub verbose: bool,

    /// Quiet mode (minimal output)
    #[arg(short = 'q', long = "quiet")]
    pub quiet: bool,
}

impl Args {
    /// Validate command line arguments
    pub fn validate(&self) -> Result<(), String> {
        // Check for conflicting file handling options
        let conflict_options = [
            self.skip_existing,
            self.overwrite_existing,
            self.rename_existing,
        ];
        let conflict_count = conflict_options.iter().filter(|&&x| x).count();

        if conflict_count > 1 {
            return Err("Only one file conflict handling option can be specified".to_string());
        }

        // Check for conflicting verbosity options
        if self.verbose && self.quiet {
            return Err("Cannot specify both --verbose and --quiet".to_string());
        }

        // In non-interactive mode, UID is required
        if !self.interactive && self.uid.is_none() {
            return Err("UID is required in non-interactive mode".to_string());
        }

        // Validate delay format if provided
        if let Some(delay) = &self.delay {
            if let Err(e) = crate::utils::helpers::parse_delay_range(delay) {
                return Err(format!("Invalid delay format: {}", e));
            }
        }

        // Validate concurrency
        if let Some(concurrency) = self.concurrency {
            if concurrency == 0 {
                return Err("Concurrency must be greater than 0".to_string());
            }
        }

        Ok(())
    }

    /// Check if we should run in interactive mode
    pub fn should_run_interactive(&self) -> bool {
        self.interactive || self.uid.is_none()
    }

    /// Get the effective log level considering verbose/quiet flags
    pub fn effective_log_level(&self) -> String {
        if self.verbose {
            "debug".to_string()
        } else if self.quiet {
            "warn".to_string()
        } else {
            self.log_level.clone().unwrap_or_else(|| "info".to_string())
        }
    }

    /// Parse tags from the tags string
    pub fn parse_tags(&self) -> Vec<String> {
        self.tags
            .as_ref()
            .map(|tags| {
                tags.split(',')
                    .map(|tag| tag.trim().to_string())
                    .filter(|tag| !tag.is_empty())
                    .collect()
            })
            .unwrap_or_default()
    }

    /// Get artwork types or default to all types
    pub fn get_artwork_types(&self) -> Vec<ArtworkType> {
        self.artwork_type
            .clone()
            .unwrap_or_else(|| vec![ArtworkType::Illust, ArtworkType::Manga, ArtworkType::Novel])
    }
}

// Implement ValueEnum for ArtworkType to work with clap
impl ValueEnum for ArtworkType {
    fn value_variants<'a>() -> &'a [Self] {
        &[Self::Illust, Self::Manga, Self::Novel, Self::Ugoira]
    }

    fn to_possible_value(&self) -> Option<clap::builder::PossibleValue> {
        Some(match self {
            Self::Illust => clap::builder::PossibleValue::new("illust"),
            Self::Manga => clap::builder::PossibleValue::new("manga"),
            Self::Novel => clap::builder::PossibleValue::new("novel"),
            Self::Ugoira => clap::builder::PossibleValue::new("ugoira"),
        })
    }
}

// Implement ValueEnum for DownloadMethod to work with clap
impl ValueEnum for DownloadMethod {
    fn value_variants<'a>() -> &'a [Self] {
        &[Self::Direct, Self::Aria2c, Self::Aria2Rpc]
    }

    fn to_possible_value(&self) -> Option<clap::builder::PossibleValue> {
        Some(match self {
            Self::Direct => clap::builder::PossibleValue::new("direct"),
            Self::Aria2c => clap::builder::PossibleValue::new("aria2c"),
            Self::Aria2Rpc => clap::builder::PossibleValue::new("aria2-rpc"),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use clap::Parser;

    #[test]
    fn test_args_parsing() {
        let args = Args::try_parse_from(&[
            "pixiv-tag-downloader",
            "--uid",
            "12345",
            "--tags",
            "tag1,tag2",
            "--logic",
            "and",
        ])
        .unwrap();

        assert_eq!(args.uid, Some(12345));
        assert_eq!(args.tags, Some("tag1,tag2".to_string()));
        assert_eq!(args.logic, Some("and".to_string()));
    }

    #[test]
    fn test_parse_tags() {
        let args = Args {
            tags: Some("tag1, tag2 , tag3".to_string()),
            ..Default::default()
        };

        let parsed_tags = args.parse_tags();
        assert_eq!(parsed_tags, vec!["tag1", "tag2", "tag3"]);
    }

    #[test]
    fn test_validation_conflicting_options() {
        let args = Args {
            skip_existing: true,
            overwrite_existing: true,
            ..Default::default()
        };

        assert!(args.validate().is_err());
    }

    #[test]
    fn test_validation_missing_uid() {
        let args = Args {
            interactive: false,
            uid: None,
            ..Default::default()
        };

        assert!(args.validate().is_err());
    }
}

// Provide a default implementation for testing
#[cfg(test)]
impl Default for Args {
    fn default() -> Self {
        Self {
            uid: None,
            tags: None,
            logic: Some("or".to_string()),
            download_all: false,
            artwork_type: None,
            cookie_file: None,
            output_dir: None,
            config: None,
            download_method: None,
            aria2_rpc_url: None,
            aria2_rpc_secret: None,
            concurrency: None,
            delay: None,
            skip_existing: false,
            overwrite_existing: false,
            rename_existing: false,
            log_level: Some("info".to_string()),
            interactive: false,
            dry_run: false,
            verbose: false,
            quiet: false,
        }
    }
}
