{"rustc": 13226066032359371072, "features": "[\"default\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 5676177281124120482, "path": 13211828935325298656, "deps": [[555019317135488525, "regex_automata", false, 8340585934847926174], [2779309023524819297, "aho_corasick", false, 16902992492458900847], [3129130049864710036, "memchr", false, 18169542468515722332], [9408802513701742484, "regex_syntax", false, 992572420482318836]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-e23a65ce993894f0/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}