{"rustc": 13226066032359371072, "features": "[\"__tls\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"hyper-tls\", \"json\", \"native-tls-crate\", \"serde_json\", \"stream\", \"tokio-native-tls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 5676177281124120482, "path": 6994241218797547596, "deps": [[40386456601120721, "percent_encoding", false, 6810750989860477491], [95042085696191081, "ipnet", false, 9841415380404347635], [264090853244900308, "sync_wrapper", false, 10991555874354020920], [784494742817713399, "tower_service", false, 7598221557306017422], [1288403060204016458, "tokio_util", false, 14276664312224584952], [1906322745568073236, "pin_project_lite", false, 15650408846425470253], [2779053297469913730, "cookie_crate", false, 13056369900335095982], [3150220818285335163, "url", false, 9007963826262921411], [3722963349756955755, "once_cell", false, 8503763199059954149], [4405182208873388884, "http", false, 17206942437042796780], [5986029879202738730, "log", false, 14167271303432483892], [7414427314941361239, "hyper", false, 14066001409188017086], [7620660491849607393, "futures_core", false, 16209081794439268000], [8915503303801890683, "http_body", false, 14159126273602268726], [9538054652646069845, "tokio", false, 13743195215501804175], [9689903380558560274, "serde", false, 2183840994822341361], [10229185211513642314, "mime", false, 17904433293886181091], [10629569228670356391, "futures_util", false, 6342917632133773089], [12186126227181294540, "tokio_native_tls", false, 5378206190471297096], [12367227501898450486, "hyper_tls", false, 11374851347391416006], [13809605890706463735, "h2", false, 7671901635003698507], [14564311161534545801, "encoding_rs", false, 13527941569555271042], [15367738274754116744, "serde_json", false, 13093148885263579207], [16066129441945555748, "bytes", false, 2962087175848418224], [16311359161338405624, "rustls_pemfile", false, 7290903158691206438], [16542808166767769916, "serde_urlencoded", false, 6654956936546850107], [16785601910559813697, "native_tls_crate", false, 5847996397127963510], [17973378407174338648, "cookie_store", false, 6118072194116454049], [18066890886671768183, "base64", false, 17113068789765177535]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/reqwest-e0200bba04184eda/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}