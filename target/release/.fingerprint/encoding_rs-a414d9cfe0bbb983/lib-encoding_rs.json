{"rustc": 13226066032359371072, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"any_all_workaround\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"serde\", \"simd-accel\"]", "target": 17616512236202378241, "profile": 5676177281124120482, "path": 11215007723683198704, "deps": [[10411997081178400487, "cfg_if", false, 5622190431807319137]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/encoding_rs-a414d9cfe0bbb983/dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}