{"rustc": 13226066032359371072, "features": "[\"color\", \"default\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"suggestions\", \"usage\"]", "declared_features": "[\"cargo\", \"color\", \"debug\", \"default\", \"deprecated\", \"derive\", \"env\", \"error-context\", \"help\", \"std\", \"string\", \"suggestions\", \"unicode\", \"unstable-derive-ui-tests\", \"unstable-doc\", \"unstable-ext\", \"unstable-markdown\", \"unstable-styles\", \"unstable-v5\", \"usage\", \"wrap_help\"]", "target": 4238846637535193678, "profile": 16322474672012669546, "path": 15165250886047578241, "deps": [[3019522439560520108, "clap_builder", false, 18378162909741995326], [17056525256108235978, "clap_derive", false, 4495097827049777582]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/clap-ff9b14c1e7054caa/dep-lib-clap", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}