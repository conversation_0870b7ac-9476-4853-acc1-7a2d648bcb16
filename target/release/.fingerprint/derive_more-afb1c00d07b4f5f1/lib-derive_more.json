{"rustc": 13226066032359371072, "features": "[\"add\", \"add_assign\"]", "declared_features": "[\"add\", \"add_assign\", \"as_mut\", \"as_ref\", \"constructor\", \"convert_case\", \"default\", \"deref\", \"deref_mut\", \"display\", \"error\", \"from\", \"from_str\", \"generate-parsing-rs\", \"index\", \"index_mut\", \"into\", \"into_iterator\", \"is_variant\", \"iterator\", \"mul\", \"mul_assign\", \"nightly\", \"not\", \"peg\", \"rustc_version\", \"sum\", \"testing-helpers\", \"track-caller\", \"try_into\", \"unwrap\"]", "target": 12153973509411789784, "profile": 17984201634715228204, "path": 17068055796779208424, "deps": [[3060637413840920116, "proc_macro2", false, 17355796404307958077], [17990358020177143287, "quote", false, 638818482820073155], [18149961000318489080, "syn", false, 9648092135361572827]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/derive_more-afb1c00d07b4f5f1/dep-lib-derive_more", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}