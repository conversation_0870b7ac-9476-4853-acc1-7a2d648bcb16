{"rustc": 13226066032359371072, "features": "[\"alloc\", \"default\", \"std\"]", "declared_features": "[\"alloc\", \"debug\", \"default\", \"simd\", \"std\", \"unstable-doc\", \"unstable-recover\"]", "target": 13376497836617006023, "profile": 10175409778378686000, "path": 2638420001767067798, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/winnow-7bb792f1263a9f7b/dep-lib-winnow", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}