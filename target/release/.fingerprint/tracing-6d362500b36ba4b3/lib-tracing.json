{"rustc": 13226066032359371072, "features": "[\"attributes\", \"default\", \"std\", \"tracing-attributes\"]", "declared_features": "[\"async-await\", \"attributes\", \"default\", \"log\", \"log-always\", \"max_level_debug\", \"max_level_error\", \"max_level_info\", \"max_level_off\", \"max_level_trace\", \"max_level_warn\", \"release_max_level_debug\", \"release_max_level_error\", \"release_max_level_info\", \"release_max_level_off\", \"release_max_level_trace\", \"release_max_level_warn\", \"std\", \"tracing-attributes\", \"valuable\"]", "target": 5568135053145998517, "profile": 3541797763817303166, "path": 7372102101121122200, "deps": [[1906322745568073236, "pin_project_lite", false, 15650408846425470253], [2967683870285097694, "tracing_attributes", false, 13366153786449936745], [11033263105862272874, "tracing_core", false, 17294295834620578958]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/tracing-6d362500b36ba4b3/dep-lib-tracing", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}