{"rustc": 13226066032359371072, "features": "[\"default\"]", "declared_features": "[\"aws-lc\", \"bindgen\", \"default\", \"unstable_boringssl\", \"v101\", \"v102\", \"v110\", \"v111\", \"vendored\"]", "target": 17474193825155910204, "profile": 5676177281124120482, "path": 7919241005001572431, "deps": [[2924422107542798392, "libc", false, 2445886996487936512], [3722963349756955755, "once_cell", false, 8503763199059954149], [6635237767502169825, "foreign_types", false, 12559698150739195642], [7896293946984509699, "bitflags", false, 16822279893269636948], [8607891082156236373, "build_script_build", false, 9840555480811596612], [9070360545695802481, "ffi", false, 11649298463313445838], [10099563100786658307, "openssl_macros", false, 17208955963699563133], [10411997081178400487, "cfg_if", false, 5622190431807319137]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/openssl-5d1568a2625cd7d2/dep-lib-openssl", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}