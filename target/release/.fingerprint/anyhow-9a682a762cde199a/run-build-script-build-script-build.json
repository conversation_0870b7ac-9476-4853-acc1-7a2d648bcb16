{"rustc": 13226066032359371072, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13625485746686963219, "build_script_build", false, 15665384351118652815]], "local": [{"RerunIfChanged": {"output": "release/build/anyhow-9a682a762cde199a/output", "paths": ["src/nightly.rs"]}}, {"RerunIfEnvChanged": {"var": "RUSTC_BOOTSTRAP", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}