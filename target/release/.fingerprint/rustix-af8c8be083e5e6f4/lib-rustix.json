{"rustc": 13226066032359371072, "features": "[\"alloc\", \"default\", \"fs\", \"std\"]", "declared_features": "[\"all-apis\", \"alloc\", \"compiler_builtins\", \"core\", \"default\", \"event\", \"fs\", \"io_uring\", \"libc\", \"libc_errno\", \"linux_4_11\", \"linux_5_1\", \"linux_5_11\", \"linux_latest\", \"mm\", \"mount\", \"net\", \"param\", \"pipe\", \"process\", \"pty\", \"rand\", \"runtime\", \"rustc-dep-of-std\", \"rustc-std-workspace-alloc\", \"shm\", \"std\", \"stdio\", \"system\", \"termios\", \"thread\", \"time\", \"try_close\", \"use-explicitly-provided-auxv\", \"use-libc\", \"use-libc-auxv\"]", "target": 16221545317719767766, "profile": 12143647723700624399, "path": 7882377925438118486, "deps": [[7896293946984509699, "bitflags", false, 16822279893269636948], [12053020504183902936, "build_script_build", false, 6987404855207844052], [12846346674781677812, "linux_raw_sys", false, 1933109096428198784]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rustix-af8c8be083e5e6f4/dep-lib-rustix", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}