{"rustc": 13226066032359371072, "features": "[\"alloc\", \"async-await\", \"default\", \"executor\", \"futures-executor\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"bilock\", \"cfg-target-has-atomic\", \"compat\", \"default\", \"executor\", \"futures-executor\", \"io-compat\", \"std\", \"thread-pool\", \"unstable\", \"write-all-vectored\"]", "target": 7465627196321967167, "profile": 18134297140301713016, "path": 11770881014501644695, "deps": [[5103565458935487, "futures_io", false, 13830099460558403649], [1811549171721445101, "futures_channel", false, 9931208240901843341], [7013762810557009322, "futures_sink", false, 18253599374235138956], [7620660491849607393, "futures_core", false, 16209081794439268000], [10629569228670356391, "futures_util", false, 6342917632133773089], [12779779637805422465, "futures_executor", false, 610004935880192307], [16240732885093539806, "futures_task", false, 3371548661070552724]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/futures-53974539f1e5633e/dep-lib-futures", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}